# Augment Guidelines for Quotation Pro Project

This document provides comprehensive guidelines for AI agents working with the Quotation Pro project. It outlines the project structure, key components, security considerations, and best practices for making changes to the codebase.

## Project Overview

Quotation Pro is a **multi-tenant business management API** built on the Yii2 PHP framework for creating quotations, invoices, budgets, and purchase orders with **complete data isolation between businesses**. The application serves as a comprehensive business document management system with advanced PDF generation, email notifications, and subscription management.

### Core Architecture

The application follows a **modular architecture** with these primary components:

- **API Module** (Primary Focus): RESTful API for mobile and web clients
- **Common Module** (Primary Focus): Shared models, components, and services
- **Console Module** (Primary Focus): Command-line utilities and scheduled tasks
- **Templates** (Primary Focus): PDF templates for various document types
- **Backend Module** (Secondary Focus): Administrative interface

### Multi-Tenant Design

- **Business Entity**: Central data model with all business-specific data linked to Business entity
- **User-Business Relationship**: Many-to-many relationship via user_business junction table
- **Data Isolation**: Complete separation through businessId foreign key in all business-related tables
- **Role-Based Access**: Business owners have full access, staff users have limited permissions

## Key Functionalities

### Document Management
- **Quotations**: Create, manage, and send quotations to clients with PDF generation
- **Invoices**: Generate invoices from quotations with GST support for Indian businesses
- **Budgets**: Plan and manage budgets for projects
- **Purchase Orders**: Manage purchase orders for suppliers
- **Proforma Invoices**: Handle proforma invoices with proper workflow
- **Delivery Notes**: Generate and manage delivery notes
- **Receipts**: Create and manage payment receipts

### Business Management
- **Customer Management**: Customer database with contact information and history
- **Product Catalog**: Product and service listings with pricing and inventory
- **User Management**: Role-based access control with business owners and staff
- **Business Settings**: Configurable business profiles, tax settings, and document numbering

### Technical Features
- **PDF Generation**: Template-based PDF generation with mPDF library and multiple design variants
- **Email System**: SMTP load balancing with 300 emails per account limit and template-based composition
- **Subscription Management**: Stripe and Razorpay integration with usage tracking and plan limitations
- **Multi-Tenant Architecture**: Complete data isolation between businesses
- **API Authentication**: Token-based authentication with AUTH_TOKEN header validation
- **Configuration System**: Layered configuration with environment-specific settings

## Directory Structure

```
quotation-pro/
├── api/                    # API implementation
│   ├── config/             # API-specific configuration
│   ├── modules/            # API modules (versioned)
│   │   └── v1/             # Version 1 of the API
│   │       ├── controllers/# API controllers
│   │       └── models/     # API-specific models
│   └── web/                # Web entry point for API
├── common/                 # Shared code
│   ├── app-config/         # Application-specific configurations
│   ├── components/         # Reusable components
│   ├── config/             # Common configuration
│   │   └── apps-settings/  # Environment-specific settings
│   ├── helpers/            # Utility functions
│   ├── mail/               # Email templates
│   ├── models/             # Data models
│   │   ├── base/           # Base models (Gii-generated)
│   │   ├── enum/           # Enumeration classes
│   │   ├── query/          # Query classes
│   │   └── traits/         # Shared traits
│   └── services/           # Service classes
├── console/                # Command-line utilities
│   ├── controllers/        # Console command controllers
│   ├── migrations/         # Database migrations
│   └── models/             # Console-specific models
├── templates/              # PDF templates
│   ├── budget/             # Budget templates
│   ├── delivery_note/      # Delivery note templates
│   ├── invoice/            # Invoice templates
│   ├── proforma_invoice/   # Proforma invoice templates
│   ├── purchase_order/     # Purchase order templates
│   ├── quotation/          # Quotation templates
│   └── receipt/            # Receipt templates
└── docs/                   # Documentation files
```

## Documentation Structure

The documentation is organized into several key files in the `docs/` directory:

1. **README.md**: Main entry point for comprehensive API documentation
2. **PROJECT_DOCUMENTATION.md**: Overview of project architecture and components
3. **project_overview.md**: Detailed system architecture, data flow, and entity relationships
4. **project_structure.md**: Directory structure and file organization
5. **configuration_system.md**: Layered configuration system and environment settings
6. **pdf_generation_system.md**: PDF template system and generation process
7. **email_system_documentation.md**: Email system architecture and components
8. **API_DOCUMENTATION.md**: Comprehensive API endpoints and usage
9. **API_CONFIGURATION_GUIDE.md**: Configuration system guide
10. **API_DEVELOPMENT_PROMPT.md**: API development guidelines
11. **MODEL_DEVELOPMENT_PROMPT.md**: Model development guidelines
12. **AI_AGENT_GUIDE.md**: Guidelines for AI agents working on the project
13. **security/README.md**: Security documentation and guidelines

## Critical Security Considerations

⚠️ **IMPORTANT**: The following critical security issues have been identified and must be addressed:

### Immediate Security Concerns
1. **Hardcoded Master Password**: `masterPassword => 'qpro.umn.nik@titanium#608'` in `common/config/params.php` (Line 202)
2. **Firebase Keys in Version Control**: Firebase server key exposed in params.php (Line 69)
3. **Master Password Authentication Bypass**: Allows login without proper password validation in UserController
4. **SQL Injection Risks**: Raw SQL execution in `execSql()` function and dynamic query building
5. **Weak Password Requirements**: Only 6 characters minimum requirement

### Security Best Practices
- **Never commit sensitive data** to version control
- **Use environment variables** for all credentials and API keys
- **Implement proper input validation** for all user inputs
- **Use parameterized queries** to prevent SQL injection
- **Add rate limiting** on authentication endpoints
- **Implement CSRF protection** for web forms

## Key Subsystems

### Email System

The email system has been refactored to follow a modular architecture with clear separation of concerns:

1. **EmailClient**: Handles mailer initialization, SMTP configuration, and low-level email operations
2. **EmailService**: Focuses on email composition and sending, using EmailClient for the underlying infrastructure

#### EmailClient Features
- Singleton pattern implementation for consistent configuration
- SMTP management with multiple providers and load balancing
- Mailer factory methods for different types of mailers
- Email tracking with counters for sent emails
- Rate limiting for SMTP providers
- Urgent admin notifications

#### EmailService Features
- Email composition with template support
- Specialized methods for different types of emails
- Error handling with centralized logging
- User and admin email sending capabilities

#### Email System Usage
- Create a new `EmailService` instance for email operations (not a singleton)
- Use `EmailClient` only for low-level mailer operations
- Follow the existing template structure for new email templates
- Always wrap email sending in try-catch blocks and log errors

### PDF Generation System

- **Template-based using mPDF library**: Uses mPDF for high-quality PDF generation
- **Multiple Template Variants**: Each document type has multiple design variants (default, modern, etc.)
- **GST Template Support**: For invoice and proforma_invoice, there are two template files:
  - `pdfView.php`: Standard template
  - `pdfView-gst.php`: GST-specific template for Indian businesses
  - Template selection based on `isGstApplicable` condition on the business
- **Dynamic Content Generation**: Templates use PHP variables for dynamic content
- **Business Settings Integration**: Template behavior controlled by business-specific settings
- **Conditional Display Elements**: Features like signature blocks, bank details, UPI details controlled by settings
- **Support for Custom Headers, Footers, and Watermarks**: Configurable branding elements

### Configuration System

**Layered Configuration Approach**:
1. **Base Configuration**: `common/config/main.php` and `common/config/params.php`
2. **Environment Configuration**: `common/config/apps-settings/{APP_ID}/main.php` and `params.php`
3. **Application Settings**: Dynamic settings stored in database with defaults in `app-settings-params.php`
4. **Local Overrides**: Developer-specific settings in `*-local.php` files (not in version control)

**Environment Support**:
- `quotation-dev`: Development environment
- `quotation-prod`: Production environment
- `quotation-local`: Local development environment
- `quotation-premium`: Premium version
- `estimate-prod`: Estimate Maker variant

**Business Settings**: Stored in database with group/key/value pattern (app, quotation, invoice, purchase_order, custom groups)

### Multi-Tenant Architecture

- **Business Entity**: Central data model with all business-specific data linked to Business entity
- **Data Isolation**: Complete separation through businessId foreign key in all business-related tables
- **User-Business Relationship**: Many-to-many via user_business junction table
- **Access Control**: Business owners have full access, staff users have role-based permissions
- **Business Settings**: Configurable settings stored in database accessible via `Business::config()` method

### API Architecture

- **RESTful Design**: API versioning (currently v1) with consistent endpoint structure
- **Authentication**: Token-based authentication via AUTH_TOKEN header
- **Base Controller**: `BaseApiController` provides consistent response formatting and error handling
- **Identity System**: `Identity` class implements Yii2's IdentityInterface for user authentication
- **Response Format**: Standardized JSON responses with success/error status, data, timestamp, and execution time
- **Multi-Tenant Aware**: All API requests filtered by business context

### Document Generation Flow

**Standard Flow**: Client Request → Authentication → Business Context → Data Validation → Document Creation → PDF Generation → Response with document data and PDF URL

**Components**:
- **Document Models**: Quotation, Invoice, Receipt, etc. with business logic
- **PdfService**: Singleton service for PDF generation using mPDF library
- **Template System**: Multiple variants per document type with customizable styling
- **File Management**: Organized storage with business-specific directories

## Developer Onboarding

### Structured Onboarding Process

**MANDATORY STEPS for New Developers:**

1. **Repository Setup**
   ```bash
   git clone [repository-url]
   cd quotation-pro
   ```

2. **Dependency Installation**
   ```bash
   composer install
   npm install  # If frontend assets exist
   ```

3. **Environment Configuration**
   - Copy `common/config/main-local.php.example` to `common/config/main-local.php`
   - Copy `common/config/params-local.php.example` to `common/config/params-local.php`
   - Set appropriate `APP_ID` in `main-local.php`
   - Configure database connection settings

4. **Database Setup**
   ```bash
   ./yii migrate
   ./yii batch-update/sync-app-settings
   ./yii batch-update/sync-business-settings
   ```

5. **Application Initialization**
   - Verify API endpoints are accessible
   - Test authentication with sample tokens
   - Validate PDF generation functionality

6. **Development Environment Verification**
   - Test document generation (quotation, invoice)
   - Verify email system functionality
   - Check multi-tenant data isolation

### Key Configuration Files

**Core Configuration:**
- **`common/config/main.php`** - Core application configuration
- **`common/config/main-local.php`** - Environment-specific configuration (not in version control)
- **`common/config/params.php`** - Global parameters and settings
- **`common/config/params-local.php`** - Environment-specific parameters (not in version control)

**Environment-Specific Settings:**
- **`common/config/apps-settings/{APP_ID}/main.php`** - App-specific configuration
- **`common/config/apps-settings/{APP_ID}/params.php`** - App-specific parameters

### Multi-Environment Configuration

**Supported Environments:**
- **`quotation-dev`**: Development environment
- **`quotation-prod`**: Production environment
- **`quotation-local`**: Local development environment
- **`quotation-premium`**: Premium version
- **`estimate-prod`**: Estimate Maker variant

**Environment Selection:**
- Defined by `APP_ID` in `main-local.php`
- Each environment has specific database and service configurations
- Settings cascade from base → environment-specific → local overrides

### Initial Learning Path

**Week 1 - Foundation:**
1. Read all documentation in `/docs/` directory
2. Understand multi-tenant architecture
3. Study authentication and authorization patterns
4. Review API structure and BaseApiController

**Week 2 - Core Systems:**
1. Study email system architecture (EmailClient vs EmailService)
2. Understand PDF generation system and templates
3. Review business settings and configuration system
4. Practice with document generation workflows

**Week 3 - Development Patterns:**
1. Study existing controller patterns
2. Understand model relationships and business scoping
3. Practice implementing new features following existing patterns
4. Review security considerations and best practices

## Development Guidelines

### General Approach

1. **Understand Before Changing**: Always thoroughly understand the existing code before making changes
2. **Detailed Planning**: Create a detailed plan before implementing changes
3. **Conservative Changes**: Be conservative with changes, especially to core components
4. **Documentation Updates**: Keep documentation updated with any code changes
5. **Testing**: Test changes thoroughly before considering them complete
6. **Follow DRY Principal**: Avoid repeating yourself in code
7. **SOLID Principal**: Adhere to SOLID principles for code design
8. **Recheck**: After making changes, recheck the implementation thoroughly to ensure it works as expected also check if you can apply DRY in implemented code and refactor.

### Guided Development Process

**MANDATORY Process for All Development Tasks:**

#### Step 1: Context Analysis and Preparation
```bash
# 1. Re-read .gitignore to focus on relevant files
# 2. Identify priority directories: api/, common/, console/, templates/
# 3. Process new or modified files from prioritized directories first
```

#### Step 2: Information Gathering
1. **Read Relevant Documentation**: Start with `/docs/` directory files
2. **Understand Business Context**: Identify which business entities are affected
3. **Examine Related Files**: Look at dependencies and interactions
4. **Check Recent Changes**: Review recent commits and modifications

#### Step 3: Context Consolidation
1. **Merge Information**: Combine newly processed information with existing context
2. **Identify Conflicts**: Note any discrepancies between stored context and recent updates
3. **Request Clarifications**: Ask targeted questions rather than re-reading files
4. **Update Mental Model**: Ensure understanding is current and complete

#### Step 4: Planning and Implementation
1. **Create Detailed Plan**: Step-by-step implementation approach
2. **Follow Existing Patterns**: Use established codebase patterns
3. **Implement Incrementally**: Make small, testable changes
4. **Maintain Context Awareness**: Reference current project context in all decisions

#### Step 5: Validation and Documentation
1. **Test Thoroughly**: Verify changes work as expected
2. **Update Documentation**: Keep documentation current with changes
3. **Context-Aware Commits**: Write commit messages referencing current project context
4. **Avoid Redundancies**: Don't reprocess files unless modifications require it

### Continuous Context Utilization

**For All Development Tasks:**
- **Code Generation**: Use continuously updated memory context
- **Debugging**: Reference current project state and recent changes
- **Commit Messages**: Include context about affected systems and business logic
- **Code Reviews**: Consider impact on multi-tenant architecture and existing patterns

**Context Maintenance Rules:**
- Process files only when modifications necessitate analysis
- Maintain awareness of high-priority areas (API, common, console, templates)
- Include details about database or business settings changes
- Reference corresponding documentation sections when making changes

## Review
- Take more time to thoroughly understand the codebase structure before implementing changes
- Pay closer attention to inheritance patterns and framework-specific conventions (like Yii2's base/extended model
pattern)
- Think more carefully about database operations to ensure they're efficient and necessary
- Double-check my work for consistency and correctness before presenting solutions
- Consider the long-term maintainability of any changes I propose

### Code Style and Conventions

1. **PSR Standards**: Follow PSR-1, PSR-2, and PSR-4 coding standards
2. **Yii2 Conventions**: Follow Yii2 framework conventions for controllers, models, and views
3. **Naming Conventions**:
   - **Models**: Singular, CamelCase (e.g., `User.php`, `Quotation.php`)
   - **Controllers**: Plural, CamelCase with Controller suffix (e.g., `UsersController.php`)
   - **Services**: CamelCase with Service suffix (e.g., `EmailService.php`, `PdfService.php`)
4. **Documentation**: Use PHPDoc comments for classes, methods, and properties
5. **Type Hints**: Use PHP type for parameters and return types

### Multi-Tenant Development

1. **Business Isolation**: **ALWAYS** scope queries by businessId to maintain data isolation
2. **Business Context**: Use `$this->business` in controllers to access current business context
3. **Business-Scoped Queries**: Implement `findByBusiness()` methods in models
4. **Foreign Keys**: Add businessId foreign key to all business-specific tables
5. **Query Validation**: Ensure all database queries include business ID conditions
6. **Access Control**: Verify user has access to the business before processing requests
7. **Settings Access**: Use `Business::config()` method for business-specific settings

### Email System Development

1. **EmailClient vs EmailService**: Understand the separation of concerns between these classes
2. **Instance Creation**: Create a new `EmailService` instance for email operations instead of using a singleton
3. **Template Structure**: Follow the existing template structure for new email templates
4. **Error Handling**: Always wrap email sending in try-catch blocks and log errors
5. **Specialized Methods**: For new types of emails, add specialized methods to EmailService

### PDF Template Development

1. **Template Structure**: Follow the existing template structure for new templates
2. **Dynamic Content**: Use PHP variables for dynamic content
3. **Responsive Design**: Ensure templates work with different paper sizes and orientations
4. **Conditional Sections**: Implement conditional sections based on document data
5. **Testing**: Test templates with various data scenarios
6. **GST Template Variants**: For invoice and proforma_invoice, implement changes in both `pdfView.php` and `pdfView-gst.php` files
7. **Template Selection Logic**: Understand that template selection is based on `isGstApplicable` condition on the business
8. **Signature Block Implementation**: Use `Key::isDisplaySignatureBlock` condition consistently across all document types
9. **Settings Integration**: Ensure all display settings (signature block, bank details, UPI details) are properly integrated

### API Development

1. **Base Controller**: **ALWAYS** extend `BaseApiController` for consistent response formatting
2. **Authentication**: Use `_checkAuth()` method for token validation and business context
3. **RESTful Design**: Follow RESTful principles for endpoint design
4. **Response Format**: Use `_sendResponse()` and `_sendErrorResponse()` methods
5. **Error Handling**: Implement proper error handling with consistent error codes
6. **Input Validation**: Validate all user inputs using model validation rules
7. **Business Scoping**: Ensure all data operations are scoped to the authenticated user's business
8. **Rate Limiting**: Implement rate limiting for sensitive endpoints
9. **CORS Headers**: Configure appropriate CORS headers for cross-origin requests

### Enhanced Testing Procedures

**MANDATORY Testing Requirements:**

#### Unit Testing Standards
```bash
# Test Location and Execution
./yii test/run                    # Run all tests
./yii test/run --filter=QuotationTest  # Run specific test class
```

**Key Test Classes:**
- **`QuotationTest`**: Tests for quotation generation, numbering, and business logic
- **`InvoiceTest`**: Tests for invoice generation, numbering, and GST calculations
- **`BusinessSettingsTest`**: Tests for business settings management and retrieval
- **`EmailServiceTest`**: Tests for email composition and sending
- **`PdfServiceTest`**: Tests for PDF generation and template rendering

#### API Testing Standards
```bash
# Postman Collection Location
/docs/api-tests/quotation-pro.postman_collection.json

# Environment Variables Required
AUTH_TOKEN={{auth_token}}
BASE_URL={{base_url}}
BUSINESS_ID={{business_id}}
```

**API Testing Checklist:**
- [ ] Authentication endpoints (`/api/v1/auth/login`)
- [ ] Document creation endpoints (quotation, invoice, etc.)
- [ ] Multi-tenant data isolation verification
- [ ] Error handling and response format consistency
- [ ] Rate limiting and security headers

#### Manual Testing Checklist

**Document Generation Testing:**
- [ ] **Quotation PDF Generation**: Verify all template variants work correctly
- [ ] **Invoice PDF Generation**: Test both standard and GST templates
- [ ] **Document Numbering**: Verify sequential numbering per business
- [ ] **Template Selection**: Confirm correct template based on business settings
- [ ] **Signature Block Display**: Test conditional signature block rendering

**Payment Processing Testing:**
- [ ] **Stripe Integration**: Test payment creation, confirmation, and webhooks
- [ ] **Razorpay Integration**: Test payment flows and webhook processing
- [ ] **Subscription Management**: Test plan creation, updates, and cancellations
- [ ] **Webhook Security**: Verify signature validation for payment webhooks

**Multi-Tenant Isolation Testing:**
- [ ] **Data Isolation**: Verify no data leakage between businesses
- [ ] **User Access Control**: Test business owner vs staff permissions
- [ ] **Settings Isolation**: Confirm business-specific settings work correctly
- [ ] **Document Access**: Verify users can only access their business documents

**Email System Testing:**
- [ ] **Template Rendering**: Test all email templates with dynamic data
- [ ] **SMTP Load Balancing**: Verify rotation between SMTP providers
- [ ] **Error Handling**: Test email sending failure scenarios
- [ ] **Rate Limiting**: Verify email sending limits are enforced

**Performance Testing:**
- [ ] **Database Queries**: Check for N+1 query problems
- [ ] **API Response Times**: Verify acceptable response times under load
- [ ] **Memory Usage**: Monitor memory consumption during PDF generation
- [ ] **Concurrent Users**: Test multi-tenant performance with concurrent access

### Performance and Quality Guidelines

1. **Database Optimization**:
   - Use eager loading to avoid N+1 query problems
   - Implement proper database indexes on businessId, isDeleted, and frequently queried columns
   - Use query caching for complex queries
   - Implement pagination for large result sets

2. **Code Quality**:
   - Follow DRY (Don't Repeat Yourself) principles
   - Implement proper error handling with try-catch blocks
   - Use type hints for parameters and return types
   - Add comprehensive PHPDoc comments
   - Avoid long methods and complex nested logic

3. **Security Practices**:
   - Use parameterized queries to prevent SQL injection
   - Validate and sanitize all user inputs
   - Implement proper file upload validation
   - Use HTTPS for all API communications
   - Implement proper session management

## Recent Changes and Considerations

### Email System Split

The email system has been recently refactored to split EmailService into:

1. **EmailClient**: Handles mailer initialization, SMTP configuration, and low-level operations
2. **EmailService**: Focuses on email composition and sending, using EmailClient

When working with the email system:

- Replace `EmailService::getInstance()` with `new EmailService(true)`
- Use `EmailClient::getInstance()->getSmtpMailer()` for SMTP operations
- Use `self::$emailClient->getLocalizedDate()` for date formatting in EmailService
- Use `EmailClient::getInstance()->sendUrgentNotificationToAdmin()` for urgent admin notifications

This split improves maintainability and provides clearer separation of concerns.

### PHP 8.3 Compatibility

**Recent Fix Applied**: Fixed PHP 8.3 deprecation warnings where `round()` function was called with null values. All `round()` calls now use null coalescing operator (`??`) to provide default values.

**Example Fix**:
```php
// Before (causes deprecation warning)
$cellValue = round($invoice['totalTaxAmount'], 4);

// After (PHP 8.3 compatible)
$cellValue = round($invoice['totalTaxAmount'] ?? 0, 4);
```

**Dependency Management**:
- Several packages use `dev-master` (unstable) - consider updating to stable versions
- Regular security updates needed for packages like Stripe, Firebase, and Yii2 components
- PHP version requirement: `>=8.1` with recommendation for PHP 8.3 compatibility

## Best Practices for AI Agents

### Information Gathering

1. **Read Documentation First**: **ALWAYS** start by reading relevant documentation in the `docs/` directory
2. **Understand Multi-Tenant Context**: Understand the business context and multi-tenant implications
3. **Security Awareness**: Be aware of the critical security issues identified in this project
4. **Examine Related Files**: Look at related files to understand dependencies and interactions
5. **Check Recent Changes**: Be aware of recent changes, especially PHP 8.3 compatibility fixes
6. **Use Codebase Retrieval**: Use the codebase retrieval tool to understand existing patterns before making changes

### Planning and Implementation

1. **Create Detailed Plans**: Always create a detailed, step-by-step plan before making changes
2. **Conservative Approach**: Be conservative with changes, especially to core components
3. **Follow Patterns**: Follow existing patterns and conventions in the codebase
4. **Incremental Changes**: Make small, incremental changes rather than large rewrites
5. **Test Thoroughly**: Test changes thoroughly before considering them complete

### Code Quality

1. **Maintain Consistency**: Keep code style consistent with the existing codebase
2. **Add Documentation**: Document your changes with clear comments and PHPDoc blocks
3. **Error Handling**: Implement proper error handling with try-catch blocks and logging
4. **Performance Considerations**: Consider performance implications, especially for database queries
5. **Security First**: **ALWAYS** consider security implications, especially for API endpoints
6. **Input Validation**: Validate all user inputs using model validation rules
7. **Type Safety**: Use PHP type hints and null coalescing operators for PHP 8.3 compatibility
8. **Testing**: Suggest writing or updating tests for any code changes

### Multi-Tenant Awareness

1. **Business Isolation**: **CRITICAL** - Always consider the multi-tenant nature of the application
2. **Query Scoping**: **MANDATORY** - Ensure ALL queries are properly scoped by businessId
3. **Access Control**: Implement proper access control checks in controllers using `_checkAuth()`
4. **Settings Management**: Use `Business::config()` method for business-specific settings
5. **Data Leakage Prevention**: Never allow data from one business to be visible to another
6. **User Context**: Always verify user has access to the business before processing requests

### Communication

1. **Clear Explanations**: Provide clear explanations of your changes and reasoning
2. **Documentation Updates**: Suggest updates to documentation when necessary
3. **Alternative Approaches**: Present alternative approaches when relevant
4. **Limitations**: Be upfront about limitations or potential issues with your changes

### Environment and Configuration

1. **APP_ID Configuration**: Ensure proper APP_ID is set in `main-local.php`
2. **Environment Variables**: Use environment variables for sensitive configuration
3. **Database Configuration**: Verify database settings in environment-specific config files
4. **SMTP Configuration**: Check SMTP provider settings for email functionality
5. **File Permissions**: Ensure proper write permissions for uploads and PDF generation

## Troubleshooting Common Issues

### Multi-Tenant Issues

- **Data Leakage**: If data from other businesses is visible, check for missing businessId conditions in queries
- **Access Control**: Verify proper access control checks in controllers using `_checkAuth()`
- **Business Context**: Ensure `$this->business` is properly set in controllers
- **User Permissions**: Verify user has proper permissions for the business
- **Settings Access**: Check if business-specific settings are being accessed correctly

### Email System Issues

- **SMTP Configuration**: Check SMTP provider configuration in params
- **Template Variables**: Verify all required variables are passed to templates
- **Error Handling**: Check logs for email sending errors
- **Rate Limiting**: Monitor email sending limits and counters

### PDF Generation Issues

- **Template Paths**: Verify template paths and file existence in templates directory
- **Dynamic Data**: Ensure all required data is passed to templates
- **mPDF Configuration**: Check mPDF configuration for errors and memory limits
- **File Permissions**: Verify write permissions for PDF storage directories
- **Template Selection**: Verify correct template is selected based on business settings
- **Asset Publishing**: Ensure template assets are properly published and accessible
- **GST Template Issues**: For invoice and proforma_invoice, ensure both `pdfView.php` and `pdfView-gst.php` are updated
- **Signature Block Display**: Check `Key::isDisplaySignatureBlock` setting if signature blocks are not appearing
- **Business Settings**: Verify business-specific display settings are properly configured

### Performance Issues

- **N+1 Queries**: Use eager loading with `with()` method to avoid N+1 query problems
- **Database Indexes**: Ensure proper indexes on businessId, isDeleted, and frequently queried columns
- **Query Optimization**: Use `explain` to analyze slow queries
- **Caching**: Implement query caching for frequently accessed data
- **Memory Usage**: Monitor memory usage for large data operations



### Security Issues

- **Authentication**: Verify AUTH_TOKEN header is properly validated
- **Input Validation**: Ensure all user inputs are validated and sanitized
- **SQL Injection**: Use parameterized queries instead of string concatenation
- **File Uploads**: Validate file types, sizes, and scan for malicious content
- **Rate Limiting**: Implement rate limiting for sensitive endpoints

## Debugging and Troubleshooting

### Basic Debugging Tools

**Application Logs:**
- `runtime/logs/app.log` - General application errors
- `runtime/logs/api.log` - API request/response logs
- `runtime/logs/email.log` - Email sending logs
- `runtime/logs/pdf.log` - PDF generation logs

**Testing Endpoints:**
```bash
# API Connectivity Test
GET /api/v1/api/test

# Database Connection Test
./yii system/health-check --component=database
```

**Multi-Tenant Debugging:**
```php
// Always verify businessId scoping in queries
$query = Model::find()->where(['businessId' => $this->business->id]);
```

## Maintenance Tasks

### Essential Console Commands

**Settings Synchronization:**
```bash
# Sync application settings
./yii batch-update/sync-app-settings

# Sync business settings
./yii batch-update/sync-business-settings
```

**Basic Maintenance:**
```bash
# Clean up old files
./yii data-cleanup/cleanup-old-files --days=90

# System health check
./yii system/health-check --all
```

## Conclusion

Following these guidelines will help ensure consistent, high-quality, and **secure** contributions to the Quotation Pro project.

### Key Priorities:
1. **Security First**: Address critical security vulnerabilities before implementing new features
2. **Multi-Tenant Awareness**: Always consider data isolation and business context
3. **Understanding Before Action**: Thoroughly understand existing code and architecture before making changes
4. **Conservative Approach**: Maintain a conservative approach to modifications, especially to core components
5. **Documentation**: Keep documentation updated with any significant changes
6. **Testing**: Always suggest testing for any code changes

### Remember:
- This is a **production application** serving real businesses
- **Data security and isolation** are paramount
- **Performance and reliability** are critical for user experience
- **Code quality** affects long-term maintainability

### Quick Reference:
- **Always scope by businessId** for data isolation
- **Use BaseApiController** for all API endpoints
- **Follow existing patterns** for new features
- **Test thoroughly** before deployment
- **Document changes** appropriately

For specific implementation details, refer to the documentation in the `/docs/` directory and examine existing code patterns in the codebase.





















### Required Documentation Structure

**MANDATORY**: All changes MUST include comprehensive documentation in the `/docs` directory:

```
/docs/
├── README.md                    # Main project documentation
├── PROJECT_DOCUMENTATION.md    # Architecture overview
├── API_DOCUMENTATION.md         # Complete API reference
├── CHANGELOG.md                 # Version history and changes
├── SETUP.md                     # Installation and setup guide
├── SECURITY.md                  # Security guidelines
├── PERFORMANCE.md               # Performance optimization
├── TESTING.md                   # Testing procedures
├── TROUBLESHOOTING.md           # Common issues and solutions
├── DEPLOYMENT.md                # Deployment procedures
├── MODEL_DEVELOPMENT_PROMPT.md  # Model development guide
├── API_DEVELOPMENT_PROMPT.md    # API development guide
└── AI_AGENT_GUIDE.md            # AI agent guidelines
```

### Documentation Standards for Quotation Pro

**MANDATORY REQUIREMENTS**:
- **Every code change** MUST include corresponding documentation updates
- **New API endpoints** MUST have complete documentation with examples
- **Model changes** MUST update MODEL_DEVELOPMENT_PROMPT.md
- **Service changes** MUST include usage examples and patterns
- **All documentation** MUST be written in clear, concise Markdown
- **Code examples** MUST be tested and working
- **API endpoints** MUST include request/response examples
- **Database changes** MUST be documented in migration files

### Feature Documentation Requirements

**For every new feature, MUST create/update**:
- `/docs/API_DOCUMENTATION.md` - Document new endpoints with examples
- `/docs/PROJECT_DOCUMENTATION.md` - Update architecture if needed
- `/docs/CHANGELOG.md` - Add detailed change log entry
- `/docs/TESTING.md` - Add testing procedures for new features
- `/docs/README.md` - Update if feature affects overview
- Model/Service documentation - Update relevant development guides

### Documentation Review Process

**MANDATORY CHECKS**:
- [ ] All relevant .md files updated
- [ ] Code examples tested and working
- [ ] API examples include request/response with proper headers
- [ ] Database schema changes documented
- [ ] Migration files include proper documentation
- [ ] Links and references verified
- [ ] Spelling and grammar checked
- [ ] Documentation follows consistent formatting
- [ ] Multi-tenant implications documented
- [ ] Security considerations included

## Version Management & Release Process

### Semantic Versioning

**Version Format**: `MAJOR.MINOR.PATCH`
- **MAJOR**: Breaking changes, incompatible API changes
- **MINOR**: New features, backward-compatible functionality
- **PATCH**: Bug fixes, backward-compatible fixes

**Version Management Locations**:
- Database migrations: `console/migrations/`
- API versioning: `api/modules/v1/`, `api/modules/v2/`
- Configuration: `common/config/params.php`
- Documentation: `docs/CHANGELOG.md`

### Release Process

**MANDATORY STEPS**:
1. **Documentation Review**: Ensure all docs are updated
2. **Testing**: Run complete test suite
3. **Version Update**: Update version in all relevant files
4. **Migration Check**: Verify database migrations are ready
5. **Security Review**: Check for security implications
6. **Performance Test**: Verify performance benchmarks
7. **Deployment**: Follow deployment procedures
8. **Post-Release**: Monitor for issues and update docs

### Dependency Management

**Composer Dependencies**:
```php
// ✅ DO: Use specific version constraints
"yiisoft/yii2": "~2.0.45",
"mpdf/mpdf": "^8.1",

// ❌ DON'T: Use unstable versions in production
"some/package": "dev-master"
```

**Migration Management**:
```php
// ✅ DO: Proper migration naming
m250701_143000_add_queue_table.php

// ✅ DO: Include rollback logic
public function safeDown() {
    $this->dropTable('{{%queue}}');
}
```

## Enhanced Code Organization Standards

### Directory Structure Standards

**Quotation Pro Structure**:
```
quotation-pro/
├── api/                    # API implementation
│   ├── modules/v1/         # Versioned API modules
│   │   ├── controllers/    # API controllers
│   │   └── models/         # API-specific models
├── common/                 # Shared components
│   ├── models/             # Data models
│   │   ├── base/           # Gii-generated base models
│   │   ├── traits/         # Shared model traits
│   │   └── enum/           # Enumeration classes
│   ├── services/           # Business logic services
│   ├── helpers/            # Utility functions
│   └── components/         # Reusable components
├── console/                # CLI and migrations
├── templates/              # PDF templates
└── docs/                   # Documentation
```

### File Naming Conventions

**Controllers**:
```php
// ✅ DO: Descriptive controller names
QuotationController.php
CustomerController.php
BusinessController.php

// ❌ DON'T: Generic or unclear names
DataController.php
MainController.php
```

**Models**:
```php
// ✅ DO: Singular entity names
Quotation.php
Customer.php
Business.php

// ✅ DO: Base model inheritance
QuotationBase.php (Gii-generated)
Quotation.php (extends QuotationBase)
```

**Services**:
```php
// ✅ DO: Service suffix for business logic
EmailService.php
PdfService.php
QueueService.php

// ✅ DO: Client suffix for external integrations
EmailClient.php
StripeClient.php
```

### Class Organization Principles

**Single Responsibility**:
```php
// ✅ DO: Focused service classes
class EmailService {
    public function sendQuotationEmail($quotation) { }
    public function sendInvoiceEmail($invoice) { }
}

class PdfService {
    public function generateQuotationPdf($quotation) { }
    public function generateInvoicePdf($invoice) { }
}

// ❌ DON'T: Mixed responsibilities
class DocumentService {
    public function sendEmail() { }
    public function generatePdf() { }
    public function processPayment() { }
}
```

## Advanced DRY Implementation

### Base Classes and Traits

**Base Controller Pattern**:
```php
// ✅ DO: Common functionality in base classes
abstract class BaseApiController extends Controller {
    protected function _checkAuth() { }
    protected function _sendResponse($data, $message) { }
    protected function _sendErrorResponse($status, $message) { }
}

class QuotationController extends BaseApiController {
    public function actionCreate() {
        $user = $this->_checkAuth();
        // Specific logic here
    }
}
```

**Model Traits for Common Functionality**:
```php
// ✅ DO: Shared behavior through traits
trait SoftDeleteTrait {
    public function behaviors() {
        return [
            'softDeleteBehavior' => [
                'class' => SoftDeleteBehavior::class,
                'softDeleteAttributeValues' => ['isDeleted' => true],
            ],
        ];
    }
}

trait BusinessScopedTrait {
    public static function findByBusiness($businessId) {
        return static::find()
            ->where(['businessId' => $businessId])
            ->andWhere(['isDeleted' => 0]);
    }
}
```

### Utility Classes

**Helper Classes for Common Operations**:
```php
// ✅ DO: Static utility methods
class StringHelper {
    public static function generateSlug($text) { }
    public static function sanitizeFileName($filename) { }
}

class ValidationHelper {
    public static function validateEmail($email) { }
    public static function validatePhoneNumber($phone) { }
}

class ArrayHelper {
    public static function filterByKey($array, $key, $value) { }
    public static function groupBy($array, $key) { }
}
```

### Configuration Management

**Centralized Configuration**:
```php
// ✅ DO: Environment-aware configuration
class ConfigHelper {
    public static function get($key, $default = null) {
        return env($key, $default);
    }

    public static function getBusinessConfig($business, $key, $default = null) {
        return $business->config($key, $default);
    }
}

// ✅ DO: Configuration constants
class ConfigKeys {
    const MAINTENANCE_MODE = 'MAINTENANCE_MODE';
    const EMAIL_LIMIT_PER_ACCOUNT = 'emailLimitPerAccount';
    const PDF_STORAGE_ENABLED = 'isPdfStorageEnabled';
}
```

### Template System Patterns

**Template Inheritance**:
```php
// ✅ DO: Base template with variants
abstract class BaseDocumentTemplate {
    protected $business;
    protected $settings;

    abstract public function render($data);

    protected function getTemplatePath($variant = 'default') {
        $type = $this->getDocumentType();
        $gstSuffix = $this->business->isGstApplicable ? '-gst' : '';
        return "templates/{$type}/{$variant}/pdfView{$gstSuffix}.php";
    }
}

class QuotationTemplate extends BaseDocumentTemplate {
    protected function getDocumentType() {
        return 'quotation';
    }
}

## Enhanced Code Readability Standards

### Naming Conventions

**Variable Naming**:
```php
// ✅ DO: Descriptive variable names
$quotationData = $request->post();
$businessSettings = $this->business->businessSettings;
$customerEmail = $customer->email;

// ❌ DON'T: Unclear abbreviations
$qData = $request->post();
$bSettings = $this->business->businessSettings;
$cEmail = $customer->email;
```

**Method Naming**:
```php
// ✅ DO: Verb-noun pattern
public function generateQuotationPdf($quotation) { }
public function sendCustomerEmail($customer, $template) { }
public function validateBusinessAccess($businessId) { }

// ❌ DON'T: Unclear or generic names
public function process($data) { }
public function handle() { }
public function doStuff($params) { }
```

### Function Design Principles

**Single Responsibility**:
```php
// ✅ DO: Focused, single-purpose functions
public function calculateQuotationTotal($quotation) {
    $subtotal = $this->calculateSubtotal($quotation->quotationItems);
    $taxAmount = $this->calculateTax($subtotal, $quotation->taxPercentage);
    $discountAmount = $this->calculateDiscount($subtotal, $quotation);

    return $subtotal + $taxAmount - $discountAmount;
}

// ❌ DON'T: Functions doing too many things
public function processQuotation($quotation) {
    // Calculate totals
    // Send emails
    // Generate PDF
    // Update database
    // Log activity
}
```

**Early Returns for Clarity**:
```php
// ✅ DO: Early returns reduce nesting
public function canUserAccessBusiness($user, $businessId) {
    if (!$user) {
        return false;
    }

    if ($user->isDeleted) {
        return false;
    }

    if (!$user->business || $user->business->id !== $businessId) {
        return false;
    }

    return true;
}

// ❌ DON'T: Deep nesting
public function canUserAccessBusiness($user, $businessId) {
    if ($user) {
        if (!$user->isDeleted) {
            if ($user->business && $user->business->id === $businessId) {
                return true;
            }
        }
    }
    return false;
}
```

### Code Documentation Standards

**PHPDoc Comments**:
```php
/**
 * Generates PDF for quotation with business-specific template.
 *
 * @param Quotation $quotation The quotation to generate PDF for
 * @param string $templateVariant Template variant (default, modern, etc.)
 * @param bool $forceRegenerate Whether to force regeneration if PDF exists
 * @return string|null PDF file path on success, null on failure
 * @throws Exception When PDF generation fails
 */
public function generateQuotationPdf($quotation, $templateVariant = 'default', $forceRegenerate = false) {
    // Implementation
}
```

**Inline Comments for Complex Logic**:
```php
// Calculate tax based on business GST settings
if ($this->business->isGstApplicable) {
    // GST calculation includes CGST + SGST for Indian businesses
    $cgst = $subtotal * ($this->business->cgstRate / 100);
    $sgst = $subtotal * ($this->business->sgstRate / 100);
    $totalTax = $cgst + $sgst;
} else {
    // Standard tax calculation for non-GST businesses
    $totalTax = $subtotal * ($quotation->taxPercentage / 100);
}
```

## Enhanced Security Guidelines

### Input Validation and Sanitization

**API Input Validation**:
```php
// ✅ DO: Comprehensive input validation
public function actionCreate() {
    $user = $this->_checkAuth();
    $request = Yii::$app->request;

    // Validate required fields
    $requiredFields = ['customerId', 'quotationDate', 'quotationItems'];
    foreach ($requiredFields as $field) {
        if (!$request->post($field)) {
            return $this->_sendErrorResponse(200, "Missing required field: {$field}", 101);
        }
    }

    // Validate business access
    $customerId = (int) $request->post('customerId');
    $customer = Customer::findByPk($customerId);
    if (!$customer || $customer->businessId !== $this->business->id) {
        return $this->_sendErrorResponse(200, 'Invalid customer access', 403);
    }

    // Sanitize input data
    $quotationData = [
        'subject' => Html::encode($request->post('subject')),
        'quotationDate' => date('Y-m-d', strtotime($request->post('quotationDate'))),
        'customerId' => $customerId,
    ];
}
```

**File Upload Security**:
```php
// ✅ DO: Secure file upload handling
public function validateUploadedFile($file) {
    // Check file size
    if ($file->size > 5 * 1024 * 1024) { // 5MB limit
        throw new Exception('File size exceeds limit');
    }

    // Check file type
    $allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    if (!in_array($file->type, $allowedTypes)) {
        throw new Exception('Invalid file type');
    }

    // Check file extension
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf'];
    $extension = strtolower(pathinfo($file->name, PATHINFO_EXTENSION));
    if (!in_array($extension, $allowedExtensions)) {
        throw new Exception('Invalid file extension');
    }

    // Scan for malicious content (if available)
    if (function_exists('finfo_open')) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file->tempName);
        finfo_close($finfo);

        if (!in_array($mimeType, $allowedTypes)) {
            throw new Exception('File content does not match extension');
        }
    }

    return true;
}
```

### SQL Injection Prevention

**Parameterized Queries**:
```php
// ✅ DO: Use parameterized queries
public function findQuotationsByDateRange($businessId, $startDate, $endDate) {
    return Quotation::find()
        ->where(['businessId' => $businessId])
        ->andWhere(['between', 'quotationDate', $startDate, $endDate])
        ->andWhere(['isDeleted' => 0])
        ->all();
}

// ✅ DO: Use Yii2 query builder
public function getBusinessStatistics($businessId, $year) {
    return (new Query())
        ->select(['COUNT(*) as total', 'SUM(totalAmount) as revenue'])
        ->from('quotation')
        ->where(['businessId' => $businessId])
        ->andWhere(['YEAR(quotationDate)' => $year])
        ->andWhere(['isDeleted' => 0])
        ->one();
}

// ❌ DON'T: String concatenation in queries
public function findQuotations($businessId, $status) {
    $sql = "SELECT * FROM quotation WHERE businessId = " . $businessId . " AND status = '" . $status . "'";
    return Yii::$app->db->createCommand($sql)->queryAll(); // DANGEROUS!
}
```

### Multi-Tenant Security Enforcement

**Business Access Validation**:
```php
// ✅ DO: Always validate business access
trait BusinessAccessTrait {
    protected function validateBusinessAccess($resourceBusinessId) {
        if (!$this->business) {
            throw new ForbiddenHttpException('No business context');
        }

        if ($this->business->id !== $resourceBusinessId) {
            throw new ForbiddenHttpException('Access denied to business resource');
        }

        return true;
    }
}

// ✅ DO: Use in controllers
public function actionView($id) {
    $user = $this->_checkAuth();
    $quotation = Quotation::findByPk($id);

    if (!$quotation) {
        return $this->_sendErrorResponse(404, 'Quotation not found');
    }

    // Critical: Validate business access
    $this->validateBusinessAccess($quotation->businessId);

    return $this->_sendResponse(['quotation' => $quotation]);
}
```

## Performance Optimization Framework

### Database Optimization

**Query Optimization**:
```php
// ✅ DO: Use eager loading to prevent N+1 queries
$quotations = Quotation::find()
    ->where(['businessId' => $this->business->id])
    ->with(['customer', 'quotationItems.product', 'assignedTo'])
    ->limit(50)
    ->all();

// ❌ DON'T: Lazy loading causing N+1 queries
$quotations = Quotation::find()
    ->where(['businessId' => $this->business->id])
    ->all();

foreach ($quotations as $quotation) {
    echo $quotation->customer->name; // N+1 query!
}
```

**Pagination for Large Datasets**:
```php
// ✅ DO: Implement pagination
public function actionIndex() {
    $user = $this->_checkAuth();
    $request = Yii::$app->request;

    $page = max(1, (int) $request->get('page', 1));
    $limit = min(100, max(10, (int) $request->get('limit', 20)));
    $offset = ($page - 1) * $limit;

    $query = Quotation::find()
        ->where(['businessId' => $this->business->id])
        ->andWhere(['isDeleted' => 0])
        ->with(['customer']);

    $total = $query->count();
    $quotations = $query->offset($offset)->limit($limit)->all();

    return $this->_sendResponse([
        'quotations' => $quotations,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}
```

### Memory Management

**Large Dataset Processing**:
```php
// ✅ DO: Use batch processing for large operations
public function processBulkQuotations($quotationIds) {
    $batchSize = 100;
    $batches = array_chunk($quotationIds, $batchSize);

    foreach ($batches as $batch) {
        $quotations = Quotation::find()
            ->where(['id' => $batch])
            ->andWhere(['businessId' => $this->business->id])
            ->all();

        foreach ($quotations as $quotation) {
            $this->processQuotation($quotation);
        }

        // Free memory after each batch
        unset($quotations);
        gc_collect_cycles();
    }
}

// ✅ DO: Use generators for memory-efficient iteration
public function exportQuotationsGenerator($businessId) {
    $query = Quotation::find()
        ->where(['businessId' => $businessId])
        ->andWhere(['isDeleted' => 0])
        ->batch(100);

    foreach ($query as $quotations) {
        foreach ($quotations as $quotation) {
            yield $this->formatQuotationForExport($quotation);
        }
    }
}
```

### Caching Strategies

**Business-Specific Caching**:
```php
// ✅ DO: Cache expensive operations with business context
public function getBusinessStatistics($businessId) {
    $cacheKey = "business_stats_{$businessId}_" . date('Y-m-d');
    $cache = Yii::$app->cache;

    $stats = $cache->get($cacheKey);
    if ($stats === false) {
        $stats = $this->calculateBusinessStatistics($businessId);
        $cache->set($cacheKey, $stats, 3600); // Cache for 1 hour
    }

    return $stats;
}

// ✅ DO: Invalidate cache when data changes
public function afterSave($insert, $changedAttributes) {
    parent::afterSave($insert, $changedAttributes);

    // Invalidate business statistics cache
    $cacheKey = "business_stats_{$this->businessId}_" . date('Y-m-d');
    Yii::$app->cache->delete($cacheKey);
}



## Enhanced Error Handling and Logging

### Exception Handling Standards

**Custom Exception Classes**:
```php
// ✅ DO: Create specific exception types
class BusinessAccessException extends Exception {
    public function __construct($message = "Access denied to business resource", $code = 403) {
        parent::__construct($message, $code);
    }
}

class QuotationValidationException extends Exception {
    private $validationErrors;

    public function __construct($errors, $message = "Quotation validation failed") {
        $this->validationErrors = $errors;
        parent::__construct($message, 422);
    }

    public function getValidationErrors() {
        return $this->validationErrors;
    }
}
```

**Structured Error Handling**:
```php
// ✅ DO: Comprehensive error handling in controllers
public function actionCreate() {
    try {
        $user = $this->_checkAuth();
        $quotation = $this->createQuotation($request->post());

        return $this->_sendResponse(['quotation' => $quotation], 'Quotation created successfully');

    } catch (BusinessAccessException $e) {
        return $this->_sendErrorResponse(403, $e->getMessage(), 403);

    } catch (QuotationValidationException $e) {
        return $this->_sendErrorResponse(422, $e->getMessage(), 422, $e->getValidationErrors());

    } catch (Exception $e) {
        // Log unexpected errors
        Yii::error("Unexpected error in quotation creation: " . $e->getMessage(), __METHOD__);
        return $this->_sendErrorResponse(500, 'Internal server error', 500);
    }
}
```

### Logging Standards

**Structured Logging with Context**:
```php
// ✅ DO: Include business context in logs
class LogHelper {
    public static function logWithContext($message, $level = 'info', $context = []) {
        $logData = [
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'context' => $context
        ];

        switch ($level) {
            case 'error':
                Yii::error(json_encode($logData));
                break;
            case 'warning':
                Yii::warning(json_encode($logData));
                break;
            default:
                Yii::info(json_encode($logData));
        }
    }
}

// Usage in services
public function generateQuotationPdf($quotation) {
    try {
        LogHelper::logWithContext('Starting PDF generation', 'info', [
            'quotationId' => $quotation->id,
            'businessId' => $quotation->businessId,
            'userId' => Yii::$app->user->id
        ]);

        $pdfPath = $this->pdfService->generate($quotation);

        LogHelper::logWithContext('PDF generation completed', 'info', [
            'quotationId' => $quotation->id,
            'pdfPath' => $pdfPath
        ]);

        return $pdfPath;

    } catch (Exception $e) {
        LogHelper::logWithContext('PDF generation failed', 'error', [
            'quotationId' => $quotation->id,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        throw $e;
    }
}
```

### Graceful Degradation

**Service Fallback Patterns**:
```php
// ✅ DO: Implement graceful degradation
class EmailService {
    public function sendEmail($to, $subject, $body) {
        try {
            // Try primary email service
            return $this->primaryEmailProvider->send($to, $subject, $body);

        } catch (Exception $e) {
            Yii::warning("Primary email service failed: " . $e->getMessage());

            try {
                // Fallback to secondary provider
                return $this->secondaryEmailProvider->send($to, $subject, $body);

            } catch (Exception $e2) {
                Yii::error("All email services failed: " . $e2->getMessage());

                // Queue for later retry
                $this->queueEmailForRetry($to, $subject, $body);
                return false;
            }
        }
    }
}
```

## Quick Reference Checklist

### 🚨 MANDATORY Pre-Commit Checklist

**Documentation Requirements**:
- [ ] **REQUIRED**: All relevant .md files updated
- [ ] **REQUIRED**: Code examples tested and working
- [ ] **REQUIRED**: API examples include request/response
- [ ] **REQUIRED**: Multi-tenant implications documented
- [ ] **REQUIRED**: Security considerations included

**Code Quality Standards**:
- [ ] Follow Yii2 framework conventions
- [ ] Implement proper business scoping for multi-tenant security
- [ ] Use descriptive naming conventions
- [ ] Include comprehensive error handling
- [ ] Implement proper input validation and sanitization
- [ ] Use parameterized queries to prevent SQL injection
- [ ] Include proper PHPDoc documentation
- [ ] Follow DRY principles with base classes/traits

**Security and Performance**:
- [ ] Validate all user inputs
- [ ] Implement proper business access controls
- [ ] Use eager loading to prevent N+1 queries
- [ ] Implement pagination for large datasets
- [ ] Include proper caching strategies
- [ ] Handle exceptions gracefully with fallbacks

### Development Workflow

1. **Documentation First**: Update relevant docs before coding
2. **Security Review**: Validate multi-tenant security
3. **Performance Check**: Ensure performance benchmarks
4. **Code Review**: Follow all checklist items
5. **Integration Test**: Verify in staging environment

**No merge allowed without complete documentation.**

## Queue System Implementation Guidelines

### Queue Service Usage Patterns

**QueueService Integration**:
```php
// ✅ DO: Use QueueService for asynchronous operations
$queueService = Yii::$app->queueService;

// Push job to queue with fallback
try {
    $queueService->push(new SendUserEmailJob([
        'userId' => $user->id,
        'template' => 'welcome-email',
        'params' => ['user' => $user],
        'subject' => 'Welcome to Quotation Pro',
    ]));
} catch (Exception $e) {
    // Fallback to synchronous execution
    Yii::warning('Queue failed, executing synchronously: ' . $e->getMessage());
    $emailService->sendWelcomeEmail($user);
}
```

**Job Class Implementation**:
```php
// ✅ DO: Extend base Job class
class SendUserEmailJob extends \common\jobs\Job
{
    public $userId;
    public $template;
    public $params;
    public $subject;

    public $tries = 3;
    public $retryAfter = 60;

    public function handle($queue = null)
    {
        $user = User::findByPk($this->userId);
        if (!$user) {
            throw new Exception("User not found: {$this->userId}");
        }

        $emailService = new EmailService();
        return $emailService->sendTemplateEmail($user, $this->template, $this->params, $this->subject);
    }

    public function failed(Exception $exception)
    {
        Yii::error("Email job failed for user {$this->userId}: " . $exception->getMessage());
        // Log to admin notification system
    }
}
```

**Queue-Aware Operations**:
```php
// ✅ DO: Use queue parameter for chaining operations
public function handle($queue = null)
{
    // Process main task
    $result = $this->processMainTask();

    // Chain additional jobs if queue is available
    if ($queue && $result) {
        $queue->push(new FollowUpJob([
            'userId' => $this->userId,
            'result' => $result
        ]));
    }

    return $result;
}
```

### Console Command Patterns

**Base Console Controller Usage**:
```php
// ✅ DO: Extend BaseConsoleController for console commands
class DataCleanupController extends BaseConsoleController
{
    public function actionCleanupInactiveBusinessFiles()
    {
        $response = [
            'quotationCount' => 0,
            'invoiceCount' => 0,
            'errorCount' => 0,
            'errors' => []
        ];

        try {
            // Cleanup logic with progress tracking
            $this->stdout("Starting cleanup process...\n", Console::FG_GREEN);

            // Process in batches
            $batchSize = 100;
            $processed = 0;

            // Implementation here

            return $this->_sendResponse($response, 'Cleanup completed successfully');

        } catch (Exception $e) {
            Yii::error("Cleanup failed: " . $e->getMessage());
            return $this->_sendErrorResponse(500, $e->getMessage(), 500);
        }
    }
}
```

**Migration Patterns**:
```php
// ✅ DO: Extend BaseMigration for database migrations
class m250701_143000_add_queue_table extends \console\migrations\BaseMigration
{
    public function safeUp()
    {
        $this->createTable('{{%queue}}', [
            'id' => $this->primaryKey(),
            'channel' => $this->string()->notNull(),
            'job' => $this->binary()->notNull(),
            'pushed_at' => $this->integer()->notNull(),
            'ttr' => $this->integer()->notNull(),
            'delay' => $this->integer()->defaultValue(0),
            'priority' => $this->integer()->unsigned()->defaultValue(1024),
            'reserved_at' => $this->integer(),
            'attempt' => $this->integer(),
            'done_at' => $this->integer(),
        ]);

        $this->createIndex('idx-queue-channel', '{{%queue}}', 'channel');
        $this->createIndex('idx-queue-reserved_at', '{{%queue}}', 'reserved_at');
        $this->createIndex('idx-queue-priority', '{{%queue}}', 'priority');
    }

    public function safeDown()
    {
        $this->dropTable('{{%queue}}');
    }
}
```

## File Management and Upload Guidelines

### File Upload Security and Organization

**File Upload Validation**:
```php
// ✅ DO: Comprehensive file upload validation
public function rules()
{
    return ArrayHelper::merge(parent::rules(), [
        ['attachmentFile', 'file',
            'skipOnEmpty' => true,
            'extensions' => ['jpg', 'png', 'pdf'],
            'maxSize' => 5 * 1024 * 1024, // 5MB
            'checkExtensionByMimeType' => true,
            'when' => function ($model) {
                return in_array($model->type, ['image', 'pdf']);
            }
        ],
        ['attachmentFile', 'required', 'when' => function ($model) {
            return in_array($model->type, ['image', 'file', 'pdf']) && empty($model->fileUrl);
        }],
    ]);
}
```

**FileManager Usage Patterns**:
```php
// ✅ DO: Use FileManager for consistent file operations
class Product extends ProductBase
{
    public $coverImage;

    public function afterSave($insert, $changedAttributes)
    {
        // Use FileManager for file operations
        FileManager::saveProductImage($this);
        parent::afterSave($insert, $changedAttributes);
    }

    public function afterDelete()
    {
        // Clean up files on deletion
        FileManager::removeProductImage($this);
        parent::afterDelete();
    }
}
```

**Directory Structure Constants**:
```php
// ✅ DO: Use predefined directory constants
defined('QUOTATION_DIR') || define('QUOTATION_DIR', 'quotation/%d/%d/%d/'); // business/year/month
defined('INVOICE_DIR') || define('INVOICE_DIR', 'invoice/%d/%d/%d/');
defined('PRODUCT_DIR') || define('PRODUCT_DIR', 'product/%d/'); // product/businessId

// Usage in file operations
$relativePath = sprintf(QUOTATION_DIR, $this->business->id, date('Y'), date('m'));
$fileUrl = FileManager::saveFileContent($pdfContent, $relativePath, $fileName);
```

**File System Interface Implementation**:
```php
// ✅ DO: Use FileManagerInterface for extensibility
class CustomFileSystem implements FileManagerInterface
{
    public function upload($model, $urlField, $fileField, $relativePath, $id = null, $prefix = null, $deleteTempFile = true): bool
    {
        // Validate file
        if (!$model->$fileField || !$model->validate($fileField)) {
            return false;
        }

        // Save file with proper error handling
        try {
            $fileUrl = $this->saveFile($model->$fileField, $relativePath, $id, $prefix, $deleteTempFile);
            if ($fileUrl) {
                // Remove old file
                $this->removeFileFromURL($model->$urlField);

                // Update model
                $model->$urlField = $fileUrl;
                $model->$fileField = null;

                if (!empty($model->id)) {
                    $model->updateAttributes([$urlField]);
                }
                return true;
            }
        } catch (Exception $e) {
            Yii::error("File upload failed: " . $e->getMessage());
        }

        return false;
    }
}
```

## API Response and Pagination Standards

### Standardized API Response Format

**Response Structure**:
```php
// ✅ DO: Use consistent response format
protected function _sendResponse($body = [], $message = '', $http_status = 200)
{
    $this->response = [
        "success" => true,
        "data" => $body,
        "timestamp" => time(),
        "executionTime" => $this->logExecutionTime(),
        'message' => $message,
    ];

    Yii::$app->response->format = Response::FORMAT_JSON;
    Yii::$app->response->statusCode = $http_status;
    Yii::$app->response->data = $this->response;

    return $this->response;
}

protected function _sendErrorResponse($http_status, $message, $errorCode = null, $errors = [])
{
    $this->response = [
        "success" => false,
        "message" => $message,
        "timestamp" => time(),
        "executionTime" => $this->logExecutionTime(),
        "errorCode" => $errorCode,
    ];

    if (!empty($errors)) {
        $this->response['errors'] = $errors;
    }

    Yii::$app->response->format = Response::FORMAT_JSON;
    Yii::$app->response->statusCode = $http_status;
    Yii::$app->response->data = $this->response;

    return $this->response;
}
```

**Pagination Implementation**:
```php
// ✅ DO: Implement consistent pagination
class CustomerListing extends ActiveRecord
{
    public $offset = 0;
    public $limit = 20;
    public $calculateTotalCount = false;

    public function search()
    {
        $query = Customer::find()
            ->where(['businessId' => $this->businessId])
            ->andWhere(['isDeleted' => 0]);

        // Apply filters
        if ($this->company) {
            $query->andWhere(['like', 'company', $this->company]);
        }

        // Count total before applying pagination
        if ($this->calculateTotalCount) {
            $this->totalRecordsCount = $query->count();
        }

        // Apply pagination
        $customers = $query->offset($this->offset)
            ->limit($this->limit)
            ->all();

        $this->totalRecords = count($customers);

        return $customers;
    }

    public function pagination()
    {
        $data = [
            'offset' => $this->offset,
            'limit' => $this->limit,
            'record_sent' => $this->totalRecords,
        ];

        if ($this->calculateTotalCount) {
            $data['total'] = $this->totalRecordsCount;
        }

        return $data;
    }
}
```

**List Action Pattern**:
```php
// ✅ DO: Standardized list action implementation
public function actionList()
{
    /** @var User $user */
    $user = $this->_checkAuth();
    $params = Yii::$app->request->get();

    $listing = new CustomerListing();
    $listing->setAttributes($params);
    $listing->currentUser = $user;
    $listing->businessId = $this->business->id;

    if (!$listing->validate()) {
        return $this->_sendErrorResponse(200, $listing->getErrorSummary(true)[0], 101);
    }

    $response = [
        'customerList' => $listing->search(),
        'pagination' => $listing->pagination(),
    ];

    // Debug mode support
    if (isset($params['debug']) && YII_DEBUG) {
        $response['sql'] = $listing->sql;
    }

    return $this->_sendResponse($response, 'Customer list sent successfully');
}
```

### Data Synchronization Patterns

**Incremental Sync Implementation**:
```php
// ✅ DO: Implement incremental synchronization
public function actionSync($lastTimestamp = 0)
{
    /** @var User $user */
    $user = $this->_checkAuth();

    $query = Quotation::find()
        ->where(['businessId' => $this->business->id])
        ->andWhere(['isDeleted' => 0]);

    // Incremental sync based on timestamp
    if ($lastTimestamp > 0) {
        $query->andWhere([
            'or',
            ['>', 'UNIX_TIMESTAMP(createdAt)', $lastTimestamp],
            ['>', 'UNIX_TIMESTAMP(updatedAt)', $lastTimestamp]
        ]);
    }

    $quotations = $query->with(['customer', 'quotationItems'])
        ->limit(100) // Prevent memory issues
        ->all();

    $response = [
        'quotations' => $quotations,
        'lastTimestamp' => time(),
        'hasMore' => count($quotations) >= 100
    ];

    return $this->_sendResponse($response, 'Quotations synchronized successfully');
}

## Environment Configuration and Settings Management

### Environment-Specific Configuration

**Configuration File Structure**:
```php
// ✅ DO: Use environment-specific configuration files
// common/config/apps-settings/quotation-local/main.php
return [
    'components' => [
        'db' => [
            'class' => 'yii\db\Connection',
            'dsn' => env('DB_DSN', 'mysql:host=localhost;dbname=quotation_local'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
        ],
        'queue' => [
            'class' => \yii\queue\db\Queue::class,
            'db' => 'db',
            'tableName' => '{{%queue}}',
            'channel' => 'default',
            'mutex' => \yii\mutex\MysqlMutex::class,
        ],
    ],
];

// ❌ DON'T: Hardcode environment-specific values
return [
    'components' => [
        'db' => [
            'dsn' => 'mysql:host=localhost;dbname=quotation_local',
            'username' => 'root',
            'password' => 'hardcoded_password',
        ],
    ],
];
```

**Environment Variable Usage**:
```php
// ✅ DO: Use env() function for environment variables
$maintenanceMode = env(Key::MAINTENANCE_MODE, false);
$emailLimit = env('emailLimitPerAccount', 300);
$logUserIds = env('logUserIds', []);

// ✅ DO: Define environment keys as constants
class Key {
    const MAINTENANCE_MODE = 'MAINTENANCE_MODE';
    const LOG_VALIDATION_ERRORS = 'LOG_VALIDATION_ERRORS';
    const SKIP_ERROR_CODES = 'SKIP_ERROR_CODES';
}

// ❌ DON'T: Direct access to $_ENV or getenv()
$maintenanceMode = $_ENV['MAINTENANCE_MODE']; // Inconsistent
$emailLimit = getenv('emailLimitPerAccount'); // No default value
```

**Application Settings Management**:
```php
// ✅ DO: Use AppSettings for dynamic configuration
class AppSettings extends AppSettingsBase
{
    public static function syncSettings()
    {
        // Sync settings from configuration files
        $settings = require Yii::getAlias('@common/config/app-settings.php');

        foreach ($settings as $key => $value) {
            $setting = self::findOne(['key' => $key]) ?: new self();
            $setting->key = $key;
            $setting->value = $value;
            $setting->save();
        }
    }

    public static function getValue($key, $default = null)
    {
        $setting = self::findOne(['key' => $key]);
        return $setting ? $setting->value : $default;
    }
}
```

### Business Settings and Multi-Tenant Configuration

**Business-Specific Settings**:
```php
// ✅ DO: Use Business::config() for business settings
class Business extends BusinessBase
{
    public function config($key, $default = null)
    {
        // Check business-specific settings first
        $businessSetting = BusinessSettings::findOne([
            'businessId' => $this->id,
            'key' => $key
        ]);

        if ($businessSetting) {
            return $businessSetting->value;
        }

        // Fall back to global app settings
        return AppSettings::getValue($key, $default);
    }

    public function setConfig($key, $value)
    {
        $setting = BusinessSettings::findOne([
            'businessId' => $this->id,
            'key' => $key
        ]) ?: new BusinessSettings();

        $setting->businessId = $this->id;
        $setting->key = $key;
        $setting->value = $value;

        return $setting->save();
    }
}
```

**Settings Traits for Document Types**:
```php
// ✅ DO: Use traits for modular settings
trait QuotationSettings
{
    public static function quotation_settings(SettingsContext $context): array
    {
        $group = Key::GROUP_QUOTATION;
        return [
            Key::DISCOUNT_SETTINGS => [
                'key' => Key::DISCOUNT_SETTINGS,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "no_discount",
                'description' => "Discount calculation method",
                'options' => ['no_discount', 'percentage', 'fixed_amount']
            ],
            Key::TAX_CALCULATION => [
                'key' => Key::TAX_CALCULATION,
                'group' => $group,
                'type' => Key::STRING,
                'value' => "exclusive",
                'description' => "Tax calculation method",
                'options' => ['inclusive', 'exclusive']
            ],
        ];
    }
}
```

## Security Implementation Patterns

### Authentication and Authorization

**Token-Based Authentication**:
```php
// ✅ DO: Implement secure token authentication
class Identity implements IdentityInterface
{
    public static function findIdentityByAccessToken($token, $type = null)
    {
        // Validate token format
        if (empty($token) || !is_string($token)) {
            return null;
        }

        // Find user by token with business context
        $user = User::find()
            ->where(['accessToken' => $token])
            ->andWhere(['isDeleted' => 0])
            ->andWhere(['isActive' => 1])
            ->with('business')
            ->one();

        if (!$user || !$user->business) {
            return null;
        }

        // Check business status
        if ($user->business->isDeleted || !$user->business->isActive) {
            return null;
        }

        return $user;
    }
}
```

**Business Access Validation**:
```php
// ✅ DO: Always validate business access
trait BusinessAccessValidation
{
    protected function validateBusinessAccess($resourceBusinessId)
    {
        if (!$this->business) {
            throw new ForbiddenHttpException('No business context available');
        }

        if ($this->business->id !== $resourceBusinessId) {
            throw new ForbiddenHttpException('Access denied to business resource');
        }

        if ($this->business->isDeleted || !$this->business->isActive) {
            throw new ForbiddenHttpException('Business account is inactive');
        }

        return true;
    }
}
```

**Rate Limiting and Security Headers**:
```php
// ✅ DO: Implement rate limiting in .htaccess
# Rate limiting configuration
<IfModule mod_ratelimit.c>
    SetEnvIf Request_URI "^/api/" rate_limit
    SetOutputFilter RATE_LIMIT;rate_limit
    SetEnv rate-limit 1000
    SetEnv rate-initial-burst 50
</IfModule>

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=********; includeSubDomains"
```

### Input Validation and Sanitization

**Comprehensive Input Validation**:
```php
// ✅ DO: Validate all inputs with business context
public function actionCreate()
{
    $user = $this->_checkAuth();
    $request = Yii::$app->request;

    // Validate required fields
    $requiredFields = ['customerId', 'quotationDate', 'quotationItems'];
    foreach ($requiredFields as $field) {
        if (!$request->post($field)) {
            return $this->_sendErrorResponse(422, "Missing required field: {$field}", 101);
        }
    }

    // Validate business access for related entities
    $customerId = (int) $request->post('customerId');
    $customer = Customer::findOne([
        'id' => $customerId,
        'businessId' => $this->business->id,
        'isDeleted' => 0
    ]);

    if (!$customer) {
        return $this->_sendErrorResponse(403, 'Invalid customer access', 403);
    }

    // Sanitize input data
    $quotationData = [
        'subject' => Html::encode(trim($request->post('subject'))),
        'quotationDate' => date('Y-m-d', strtotime($request->post('quotationDate'))),
        'customerId' => $customerId,
        'businessId' => $this->business->id,
    ];

    // Validate quotation items
    $quotationItems = $request->post('quotationItems', []);
    if (!is_array($quotationItems) || empty($quotationItems)) {
        return $this->_sendErrorResponse(422, 'At least one quotation item is required', 102);
    }

    foreach ($quotationItems as $index => $item) {
        if (!isset($item['productId']) || !isset($item['quantity']) || !isset($item['rate'])) {
            return $this->_sendErrorResponse(422, "Invalid quotation item at index {$index}", 103);
        }

        // Validate product belongs to business
        $product = Product::findOne([
            'id' => (int) $item['productId'],
            'businessId' => $this->business->id,
            'isDeleted' => 0
        ]);

        if (!$product) {
            return $this->_sendErrorResponse(403, "Invalid product access at index {$index}", 403);
        }
    }
}
```

**SQL Injection Prevention**:
```php
// ✅ DO: Always use parameterized queries
public function searchQuotations($businessId, $filters = [])
{
    $query = Quotation::find()
        ->where(['businessId' => $businessId])
        ->andWhere(['isDeleted' => 0]);

    // Safe parameter binding
    if (!empty($filters['status'])) {
        $query->andWhere(['status' => $filters['status']]);
    }

    if (!empty($filters['dateFrom']) && !empty($filters['dateTo'])) {
        $query->andWhere(['between', 'quotationDate', $filters['dateFrom'], $filters['dateTo']]);
    }

    if (!empty($filters['customerName'])) {
        $query->joinWith('customer')
            ->andWhere(['like', 'customer.company', $filters['customerName']]);
    }

    return $query->all();
}

// ❌ DON'T: String concatenation in queries
public function searchQuotations($businessId, $status) {
    $sql = "SELECT * FROM quotation WHERE businessId = " . $businessId . " AND status = '" . $status . "'";
    return Yii::$app->db->createCommand($sql)->queryAll(); // DANGEROUS!
}
```

## Maintenance and Monitoring Guidelines

### Health Checks and Monitoring

**Health Check Implementation**:
```php
// ✅ DO: Implement comprehensive health checks
public function actionHealthCheck()
{
    $checks = [
        'database' => $this->checkDatabase(),
        'queue' => $this->checkQueue(),
        'storage' => $this->checkStorage(),
        'email' => $this->checkEmailService(),
    ];

    $allHealthy = array_reduce($checks, function($carry, $check) {
        return $carry && $check['status'] === 'OK';
    }, true);

    $response = [
        'status' => $allHealthy ? 'OK' : 'ERROR',
        'timestamp' => time(),
        'checks' => $checks,
    ];

    Yii::$app->response->format = Response::FORMAT_JSON;
    Yii::$app->response->statusCode = $allHealthy ? 200 : 503;

    return $response;
}

private function checkDatabase()
{
    try {
        Yii::$app->db->createCommand('SELECT 1')->queryScalar();
        return ['status' => 'OK', 'message' => 'Database connection successful'];
    } catch (Exception $e) {
        return ['status' => 'ERROR', 'message' => 'Database connection failed: ' . $e->getMessage()];
    }
}
```

**Maintenance Mode Implementation**:
```php
// ✅ DO: Implement graceful maintenance mode
public function init()
{
    parent::init();

    if (env(Key::MAINTENANCE_MODE, false)) {
        // Allow admin access during maintenance
        if (!$this->isAdminUser()) {
            return $this->_sendErrorResponse(503,
                "Application is under maintenance. Please try again later.",
                503
            );
        }
    }
}

private function isAdminUser()
{
    $user = Yii::$app->user->identity;
    if (!$user) {
        return false;
    }

    $superAdmins = Yii::$app->params['superAdmins'] ?? [];
    return in_array($user->email, $superAdmins);
}

## Logging and Debugging Guidelines

### Structured Logging Patterns

**Context-Aware Logging**:
```php
// ✅ DO: Include business and user context in logs
class LogHelper
{
    public static function logWithContext($message, $level = 'info', $context = [])
    {
        $user = Yii::$app->user->identity;
        $business = $user ? $user->business : null;

        $logData = [
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'userId' => $user ? $user->id : null,
            'businessId' => $business ? $business->id : null,
            'ip' => Yii::$app->request->userIP,
            'userAgent' => Yii::$app->request->userAgent,
            'context' => $context
        ];

        switch ($level) {
            case 'error':
                Yii::error(json_encode($logData), __METHOD__);
                break;
            case 'warning':
                Yii::warning(json_encode($logData), __METHOD__);
                break;
            default:
                Yii::info(json_encode($logData), __METHOD__);
        }
    }
}
```

**Request/Response Logging**:
```php
// ✅ DO: Log API requests for specific users
protected function logRequest()
{
    if (in_array($this->user->id ?? 0, $this->logUserIds)) {
        $requestData = [
            'method' => Yii::$app->request->method,
            'url' => Yii::$app->request->url,
            'headers' => Yii::$app->request->headers->toArray(),
            'body' => Yii::$app->request->post(),
            'timestamp' => time()
        ];

        Yii::info("API Request: " . json_encode($requestData), 'api-request');
    }
}

protected function logResponse($response)
{
    if (in_array($this->user->id ?? 0, $this->logUserIds)) {
        $responseData = [
            'response' => $response,
            'executionTime' => $this->logExecutionTime(),
            'timestamp' => time()
        ];

        Yii::info("API Response: " . json_encode($responseData), 'api-response');
    }
}
```

**Error Logging with Stack Traces**:
```php
// ✅ DO: Log errors with full context
public function handleException($exception)
{
    $errorData = [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'userId' => Yii::$app->user->id ?? null,
        'businessId' => $this->business->id ?? null,
        'request' => [
            'method' => Yii::$app->request->method,
            'url' => Yii::$app->request->url,
            'post' => Yii::$app->request->post(),
            'get' => Yii::$app->request->get(),
        ]
    ];

    Yii::error("Exception occurred: " . json_encode($errorData), __METHOD__);

    // Send to external monitoring service if configured
    if ($monitoringService = Yii::$app->get('monitoring', false)) {
        $monitoringService->reportException($exception, $errorData);
    }
}
```

### Debug Mode and Development Tools

**Debug Information in Responses**:
```php
// ✅ DO: Include debug information in development
public function actionList()
{
    // ... main logic ...

    $response = [
        'customerList' => $listing->search(),
        'pagination' => $listing->pagination(),
    ];

    // Add debug information in development
    if (YII_DEBUG && isset($params['debug'])) {
        $response['debug'] = [
            'sql' => $listing->sql,
            'executionTime' => $this->logExecutionTime(),
            'memoryUsage' => memory_get_usage(true),
            'queryCount' => count(Yii::getLogger()->messages),
        ];
    }

    return $this->_sendResponse($response, 'Customer list sent successfully');
}
```

**Performance Monitoring**:
```php
// ✅ DO: Monitor performance of critical operations
public function generateQuotationPdf($quotation)
{
    $startTime = microtime(true);
    $startMemory = memory_get_usage();

    try {
        $pdfContent = $this->pdfService->generate($quotation);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        // Log performance metrics
        LogHelper::logWithContext('PDF generation completed', 'info', [
            'quotationId' => $quotation->id,
            'executionTime' => round($endTime - $startTime, 3),
            'memoryUsed' => $endMemory - $startMemory,
            'pdfSize' => strlen($pdfContent)
        ]);

        return $pdfContent;

    } catch (Exception $e) {
        LogHelper::logWithContext('PDF generation failed', 'error', [
            'quotationId' => $quotation->id,
            'error' => $e->getMessage(),
            'executionTime' => round(microtime(true) - $startTime, 3)
        ]);
        throw $e;
    }
}
```

## Updated Quick Reference Checklist

### 🚨 MANDATORY Pre-Commit Checklist

**Documentation Requirements**:
- [ ] **REQUIRED**: All relevant .md files updated
- [ ] **REQUIRED**: Code examples tested and working
- [ ] **REQUIRED**: API examples include request/response
- [ ] **REQUIRED**: Multi-tenant implications documented
- [ ] **REQUIRED**: Security considerations included
- [ ] **REQUIRED**: Queue system usage documented if applicable
- [ ] **REQUIRED**: File upload patterns documented if applicable

**Code Quality Standards**:
- [ ] Follow Yii2 framework conventions
- [ ] Implement proper business scoping for multi-tenant security
- [ ] Use descriptive naming conventions
- [ ] Include comprehensive error handling with structured logging
- [ ] Implement proper input validation and sanitization
- [ ] Use parameterized queries to prevent SQL injection
- [ ] Include proper PHPDoc documentation
- [ ] Follow DRY principles with base classes/traits
- [ ] Use environment variables for configuration
- [ ] Implement proper file upload validation

**Security and Performance**:
- [ ] Validate all user inputs with business context
- [ ] Implement proper business access controls
- [ ] Use eager loading to prevent N+1 queries
- [ ] Implement pagination for large datasets
- [ ] Include proper caching strategies
- [ ] Handle exceptions gracefully with fallbacks
- [ ] Use FileManager for all file operations
- [ ] Implement rate limiting for API endpoints
- [ ] Include security headers in responses

**Queue and Background Processing**:
- [ ] Use QueueService for asynchronous operations
- [ ] Implement proper job classes with retry logic
- [ ] Include fallback mechanisms for queue failures
- [ ] Use console commands for batch operations
- [ ] Implement proper error handling in jobs

**API and Response Standards**:
- [ ] Use consistent response format with BaseApiController
- [ ] Implement proper pagination in list endpoints
- [ ] Include debug information in development mode
- [ ] Use incremental sync patterns for data synchronization
- [ ] Implement proper health check endpoints

**Environment and Configuration**:
- [ ] Use environment-specific configuration files
- [ ] Implement proper settings management with Business::config()
- [ ] Use constants for configuration keys
- [ ] Include maintenance mode support
- [ ] Implement proper logging with business context

### Development Workflow

1. **Documentation First**: Update relevant docs before coding
2. **Security Review**: Validate multi-tenant security and input validation
3. **Performance Check**: Ensure performance benchmarks and proper caching
4. **Queue Integration**: Use queue system for background operations
5. **File Management**: Use FileManager for all file operations
6. **Code Review**: Follow all checklist items
7. **Integration Test**: Verify in staging environment

**No merge allowed without complete documentation.**
```
```
```
```

Generate a comprehensive git commit message following conventional commit format based on the current changes to the `.augment-guidelines` file. The commit message should:

1. **Use conventional commit format**: `type(scope): description`
   - Type: `docs` (since this is documentation enhancement)
   - Scope: `guidelines` (the specific area being modified)
   - Description: Clear, concise summary of what was added/changed

2. **Include a detailed body** that explains:
   - What specific guidelines were added or enhanced
   - Why these changes were made (referencing the BlazeCommerce guidelines adaptation)
   - How this improves the development process for Quotation Pro

3. **Follow the project's established patterns** from the conversation:
   - Reference the continuous improvement framework
   - Mention the documentation-first development approach
   - Note the enhanced security and performance guidelines

4. **Structure the commit message** as:
   - Subject line (50 characters or less)
   - Blank line
   - Detailed body explaining the changes
   - Optional footer with breaking changes or references

5. **Consider the scope of changes**:
   - Integration of BlazeCommerce best practices
   - Addition of mandatory documentation requirements
   - Improved code organization and DRY principles
   - Security and performance optimization frameworks

The commit message should be professional, informative, and help future developers understand the significance of these guideline enhancements for the Quotation Pro project.